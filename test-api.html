<!DOCTYPE html>
<html>
<head>
    <title>测试反馈消息API</title>
</head>
<body>
    <h1>测试反馈消息API</h1>
    <button onclick="testAPI()">测试获取反馈消息</button>
    <div id="result"></div>

    <script>
        async function testAPI() {
            try {
                const response = await fetch('http://localhost:5000/api/admin/config-manager/feedback-messages');
                const data = await response.json();
                
                console.log('API返回数据:', data);
                
                // 分析数据
                const plans = [...new Set(data.map(item => item.subscriptionPlan))];
                const planCounts = {};
                plans.forEach(plan => {
                    planCounts[plan] = data.filter(item => item.subscriptionPlan === plan).length;
                });
                
                const resultDiv = document.getElementById('result');
                resultDiv.innerHTML = `
                    <h2>API测试结果</h2>
                    <p>总配置数量: ${data.length}</p>
                    <p>订阅计划类型: ${plans.join(', ')}</p>
                    <h3>各计划配置数量:</h3>
                    <ul>
                        ${Object.entries(planCounts).map(([plan, count]) => 
                            `<li>${plan}: ${count}条</li>`
                        ).join('')}
                    </ul>
                    <h3>详细数据:</h3>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
                
                // 检查是否还有TRIAL版本
                const hasTrialConfigs = data.some(item => item.subscriptionPlan === 'TRIAL');
                if (hasTrialConfigs) {
                    alert('❌ 仍然存在TRIAL版本配置！');
                } else {
                    alert('✅ 成功！没有TRIAL版本配置了！');
                }
                
            } catch (error) {
                console.error('API测试失败:', error);
                document.getElementById('result').innerHTML = `<p style="color: red;">API测试失败: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
