import FeedbackMessage from '../models/feedbackMessage.model';

/**
 * 检查当前数据库中的反馈配置状态
 */
async function checkCurrentFeedbackConfig() {
  console.log('=== 检查当前反馈配置状态 ===\n');

  try {
    // 1. 检查数据库中所有的反馈消息
    const allFeedbacks = await FeedbackMessage.findAll({
      where: { isActive: true },
      order: [['subscriptionPlan', 'ASC'], ['studyState', 'ASC'], ['distractedSubtype', 'ASC']]
    });

    console.log(`数据库中共有 ${allFeedbacks.length} 条反馈配置\n`);

    // 2. 按订阅计划分组
    const groupedByPlan: { [key: string]: any[] } = {};
    allFeedbacks.forEach(feedback => {
      const plan = feedback.subscriptionPlan;
      if (!groupedByPlan[plan]) {
        groupedByPlan[plan] = [];
      }
      groupedByPlan[plan].push(feedback);
    });

    console.log('按订阅计划分组:');
    Object.keys(groupedByPlan).forEach(plan => {
      console.log(`  ${plan}: ${groupedByPlan[plan].length} 条配置`);
    });

    // 3. 检查是否还有TRIAL版本的配置
    const trialConfigs = allFeedbacks.filter(f => f.subscriptionPlan === 'TRIAL');
    const proConfigs = allFeedbacks.filter(f => f.subscriptionPlan === 'PRO');
    const standardConfigs = allFeedbacks.filter(f => f.subscriptionPlan === 'STANDARD');

    console.log('\n详细分析:');
    console.log(`TRIAL版本配置: ${trialConfigs.length} 条`);
    console.log(`PRO版本配置: ${proConfigs.length} 条`);
    console.log(`STANDARD版本配置: ${standardConfigs.length} 条`);

    // 4. 如果还有TRIAL配置，显示它们
    if (trialConfigs.length > 0) {
      console.log('\n⚠️  仍然存在TRIAL版本配置:');
      trialConfigs.forEach(config => {
        console.log(`  - ${config.studyState}${config.distractedSubtype ? ` (${config.distractedSubtype})` : ''}`);
      });
      console.log('\n建议: 需要重新初始化反馈配置以清除TRIAL版本');
    } else {
      console.log('\n✅ TRIAL版本配置已正确清除');
    }

    // 5. 检查PRO配置的音频文件
    console.log('\n专业版配置的音频文件:');
    proConfigs.forEach(config => {
      const audioCount = config.audioUrls ? config.audioUrls.filter((url: string) => url && url.trim()).length : 0;
      console.log(`  ${config.studyState}${config.distractedSubtype ? ` (${config.distractedSubtype})` : ''}: ${audioCount}/${config.messages.length} 个音频文件`);
      
      if (config.audioUrls && config.audioUrls.length > 0) {
        const sampleAudio = config.audioUrls[0];
        if (sampleAudio && sampleAudio.trim()) {
          console.log(`    示例音频: ${sampleAudio}`);
        }
      }
    });

    console.log('\n=== 检查完成 ===');

  } catch (error) {
    console.error('检查失败:', error);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  checkCurrentFeedbackConfig().then(() => {
    process.exit(0);
  });
}

export { checkCurrentFeedbackConfig };
