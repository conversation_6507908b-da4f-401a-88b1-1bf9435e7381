<!DOCTYPE html>
<html>
<head>
    <title>调试音频覆盖问题</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .config { border: 1px solid #ccc; margin: 10px 0; padding: 10px; }
        .audio-list { background: #f5f5f5; padding: 5px; margin: 5px 0; }
        .duplicate { background: #ffebee; border-color: #f44336; }
        .unique { background: #e8f5e8; border-color: #4caf50; }
    </style>
</head>
<body>
    <h1>调试音频覆盖问题</h1>
    <button onclick="debugAudioIssue()">检查音频配置</button>
    <div id="result"></div>

    <script>
        async function debugAudioIssue() {
            try {
                // 尝试从localStorage获取token
                const token = localStorage.getItem('adminToken') || localStorage.getItem('token');

                const headers = {
                    'Content-Type': 'application/json'
                };

                if (token) {
                    headers['Authorization'] = `Bearer ${token}`;
                }

                const response = await fetch('http://localhost:5000/api/admin/config-manager/feedback-messages', {
                    headers: headers
                });
                const result = await response.json();

                console.log('API返回结果:', result);

                // 检查数据格式
                let data;
                if (Array.isArray(result)) {
                    data = result;
                } else if (result.data && Array.isArray(result.data)) {
                    data = result.data;
                } else {
                    throw new Error('API返回的数据格式不正确: ' + JSON.stringify(result));
                }

                console.log('解析后的数据:', data);
                
                // 分析音频URL重复情况
                const audioUrlMap = new Map();
                const duplicateGroups = [];
                
                data.forEach((config, index) => {
                    if (config.audioUrls && config.audioUrls.length > 0) {
                        const audioKey = JSON.stringify(config.audioUrls);
                        if (audioUrlMap.has(audioKey)) {
                            audioUrlMap.get(audioKey).push(config);
                        } else {
                            audioUrlMap.set(audioKey, [config]);
                        }
                    }
                });
                
                // 找出重复的音频配置
                audioUrlMap.forEach((configs, audioKey) => {
                    if (configs.length > 1) {
                        duplicateGroups.push({
                            audioUrls: JSON.parse(audioKey),
                            configs: configs
                        });
                    }
                });
                
                const resultDiv = document.getElementById('result');
                
                if (duplicateGroups.length > 0) {
                    resultDiv.innerHTML = `
                        <h2>🚨 发现音频重复问题！</h2>
                        <p>有 ${duplicateGroups.length} 组配置共享相同的音频文件</p>
                        ${duplicateGroups.map((group, index) => `
                            <div class="config duplicate">
                                <h3>重复组 ${index + 1}</h3>
                                <div class="audio-list">
                                    <strong>共享的音频文件:</strong><br>
                                    ${group.audioUrls.map(url => `• ${url}`).join('<br>')}
                                </div>
                                <strong>受影响的配置:</strong>
                                ${group.configs.map(config => `
                                    <div style="margin: 5px 0; padding: 5px; background: white;">
                                        ID: ${config.id} - ${config.subscriptionPlan} - ${config.studyState}${config.distractedSubtype ? ` (${config.distractedSubtype})` : ''}
                                    </div>
                                `).join('')}
                            </div>
                        `).join('')}
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <h2>✅ 没有发现音频重复问题</h2>
                        <p>所有配置都有独立的音频文件</p>
                        <div class="config unique">
                            <h3>配置详情</h3>
                            ${data.map(config => `
                                <div style="margin: 5px 0; padding: 5px; background: white;">
                                    <strong>ID: ${config.id}</strong> - ${config.subscriptionPlan} - ${config.studyState}${config.distractedSubtype ? ` (${config.distractedSubtype})` : ''}<br>
                                    <div class="audio-list">
                                        音频: ${config.audioUrls && config.audioUrls.length > 0 ? config.audioUrls.join(', ') : '无'}
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    `;
                }
                
                // 额外检查：查看是否有相同的音频文件名
                const allAudioFiles = [];
                data.forEach(config => {
                    if (config.audioUrls) {
                        config.audioUrls.forEach(url => {
                            if (url && url.trim()) {
                                allAudioFiles.push({
                                    url: url,
                                    configId: config.id,
                                    configType: `${config.subscriptionPlan}-${config.studyState}${config.distractedSubtype ? `-${config.distractedSubtype}` : ''}`
                                });
                            }
                        });
                    }
                });
                
                const fileNameMap = new Map();
                allAudioFiles.forEach(file => {
                    const fileName = file.url.split('/').pop();
                    if (fileNameMap.has(fileName)) {
                        fileNameMap.get(fileName).push(file);
                    } else {
                        fileNameMap.set(fileName, [file]);
                    }
                });
                
                const duplicateFiles = [];
                fileNameMap.forEach((files, fileName) => {
                    if (files.length > 1) {
                        duplicateFiles.push({ fileName, files });
                    }
                });
                
                if (duplicateFiles.length > 0) {
                    resultDiv.innerHTML += `
                        <div class="config duplicate">
                            <h3>🚨 发现重复的音频文件名</h3>
                            ${duplicateFiles.map(dup => `
                                <div style="margin: 10px 0;">
                                    <strong>文件名: ${dup.fileName}</strong><br>
                                    被以下配置使用:
                                    ${dup.files.map(file => `
                                        <div style="margin-left: 20px;">• 配置ID ${file.configId}: ${file.configType}</div>
                                    `).join('')}
                                </div>
                            `).join('')}
                        </div>
                    `;
                }
                
            } catch (error) {
                console.error('调试失败:', error);
                document.getElementById('result').innerHTML = `<p style="color: red;">调试失败: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
