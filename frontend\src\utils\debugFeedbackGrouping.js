/**
 * 调试反馈消息分组逻辑
 */

// 模拟从后端获取的数据（基于我们的修改，现在只有PRO和STANDARD）
const mockFeedbackMessages = [
  {
    id: 1,
    characterRank: 'WUKONG',
    subscriptionPlan: 'PRO',  // 现在只有PRO，没有TRIAL
    studyState: 'FOCUSED',
    messages: ['专业版专注消息1', '专业版专注消息2'],
    audioUrls: ['pro_focused_1.mp3', 'pro_focused_2.mp3'],
    isActive: true
  },
  {
    id: 2,
    characterRank: 'WUKONG',
    subscriptionPlan: 'PRO',
    studyState: 'DISTRACTED',
    distractedSubtype: 'PLAY',
    messages: ['专业版玩弄物品消息1'],
    audioUrls: ['pro_play_1.mp3'],
    isActive: true
  },
  {
    id: 3,
    characterRank: 'WUKONG',
    subscriptionPlan: 'STANDARD',
    studyState: 'FOCUSED',
    messages: ['标准版专注消息1'],
    audioUrls: ['standard_focused_1.mp3'],
    isActive: true
  }
];

function testCurrentGrouping() {
  console.log('=== 测试当前分组逻辑 ===');
  console.log('输入数据:', mockFeedbackMessages.map(f => `${f.subscriptionPlan}-${f.studyState}`));
  
  // 当前的分组逻辑
  const groupedFeedbacks = {};
  
  mockFeedbackMessages.forEach(feedback => {
    // 将试用版和专业版合并到专业版组
    const planKey = feedback.subscriptionPlan === 'TRIAL' ? 'PRO' : feedback.subscriptionPlan;
    const key = `${feedback.characterRank}-${planKey}`;
    if (!groupedFeedbacks[key]) {
      groupedFeedbacks[key] = [];
    }
    groupedFeedbacks[key].push(feedback);
  });

  console.log('\n分组结果:');
  Object.keys(groupedFeedbacks).forEach(key => {
    const [characterRank, subscriptionPlan] = key.split('-');
    const feedbacks = groupedFeedbacks[key];
    
    // 获取订阅计划名称
    const getSubscriptionName = (plan) => {
      switch(plan) {
        case 'TRIAL': return '试用版';
        case 'STANDARD': return '标准版';
        case 'PRO': return '专业版/试用版';
        default: return plan;
      }
    };
    
    console.log(`  ${characterRank} - ${getSubscriptionName(subscriptionPlan)}: ${feedbacks.length}条配置`);
    feedbacks.forEach(f => {
      console.log(`    - ${f.studyState}${f.distractedSubtype ? ` (${f.distractedSubtype})` : ''}`);
    });
  });
}

function testExpectedBehavior() {
  console.log('\n=== 期望的行为 ===');
  console.log('应该显示:');
  console.log('  悟空 - 专业版/试用版: 2条配置');
  console.log('    - FOCUSED');
  console.log('    - DISTRACTED (PLAY)');
  console.log('  悟空 - 标准版: 1条配置');
  console.log('    - FOCUSED');
}

function testSolution() {
  console.log('\n=== 解决方案验证 ===');
  console.log('问题: 后端现在只存储PRO配置，前端需要正确显示为"专业版/试用版"');
  console.log('解决方案: 前端的getSubscriptionName函数已经正确处理PRO -> "专业版/试用版"');
  console.log('状态: ✅ 应该已经正确工作');
}

// 运行测试
console.log('🔍 调试反馈消息分组逻辑\n');
testCurrentGrouping();
testExpectedBehavior();
testSolution();
