/**
 * 测试配置UI修改效果的工具函数
 */

// 模拟反馈消息数据
const mockFeedbackMessages = [
  {
    id: 1,
    characterRank: 'WUKONG',
    subscriptionPlan: 'PRO',
    studyState: 'FOCUSED',
    messages: ['专业版专注消息1', '专业版专注消息2'],
    audioUrls: ['pro_focused_1.mp3', 'pro_focused_2.mp3'],
    isActive: true
  },
  {
    id: 2,
    characterRank: 'WUKONG',
    subscriptionPlan: 'PRO',
    studyState: 'DISTRACTED',
    distractedSubtype: 'PLAY',
    messages: ['专业版玩弄物品消息1', '专业版玩弄物品消息2'],
    audioUrls: ['pro_play_1.mp3', 'pro_play_2.mp3'],
    isActive: true
  },
  {
    id: 3,
    characterRank: 'WUKONG',
    subscriptionPlan: 'STANDARD',
    studyState: 'FOCUSED',
    messages: ['标准版专注消息1', '标准版专注消息2'],
    audioUrls: ['standard_focused_1.mp3', 'standard_focused_2.mp3'],
    isActive: true
  }
];

// 模拟AI提示词配置数据
const mockAiPrompts = [
  {
    _id: '1',
    subscriptionPlan: 'PRO',
    promptTemplate: '专业版AI提示词模板...',
    maxTokens: 200,
    analysisIntervalSeconds: 15,
    isActive: true
  },
  {
    _id: '2',
    subscriptionPlan: 'STANDARD',
    promptTemplate: '标准版AI提示词模板...',
    maxTokens: 100,
    analysisIntervalSeconds: 30,
    isActive: true
  }
];

// 模拟UI文本配置数据
const mockUITextConfigs = [
  {
    _id: '1',
    subscriptionPlan: 'PRO',
    feedbackTitle: '专业版反馈标题',
    sleepMessage: '专业版睡眠消息',
    idleMessage: '专业版空闲消息',
    isActive: true
  },
  {
    _id: '2',
    subscriptionPlan: 'STANDARD',
    feedbackTitle: '标准版反馈标题',
    sleepMessage: '标准版睡眠消息',
    idleMessage: '标准版空闲消息',
    isActive: true
  }
];

/**
 * 测试反馈消息分组逻辑
 */
export function testFeedbackGrouping() {
  console.log('=== 测试反馈消息分组逻辑 ===');
  
  // 模拟修改后的分组逻辑
  const groupedFeedbacks: { [key: string]: any[] } = {};
  
  mockFeedbackMessages.forEach(feedback => {
    // 将试用版和专业版合并到专业版组
    const planKey = feedback.subscriptionPlan === 'TRIAL' ? 'PRO' : feedback.subscriptionPlan;
    const key = `${feedback.characterRank}-${planKey}`;
    if (!groupedFeedbacks[key]) {
      groupedFeedbacks[key] = [];
    }
    groupedFeedbacks[key].push(feedback);
  });

  console.log('分组结果:', Object.keys(groupedFeedbacks));
  console.log('专业版组包含:', groupedFeedbacks['WUKONG-PRO']?.length || 0, '条配置');
  console.log('标准版组包含:', groupedFeedbacks['WUKONG-STANDARD']?.length || 0, '条配置');
  
  return groupedFeedbacks;
}

/**
 * 测试订阅计划名称显示
 */
export function testSubscriptionNameDisplay() {
  console.log('\n=== 测试订阅计划名称显示 ===');
  
  const getSubscriptionName = (plan: string) => {
    switch(plan) {
      case 'TRIAL': return '试用版';
      case 'STANDARD': return '标准版';
      case 'PRO': return '专业版/试用版';
      default: return plan;
    }
  };

  const plans = ['TRIAL', 'STANDARD', 'PRO'];
  plans.forEach(plan => {
    console.log(`${plan} -> ${getSubscriptionName(plan)}`);
  });
}

/**
 * 测试配置选项简化
 */
export function testConfigOptions() {
  console.log('\n=== 测试配置选项简化 ===');
  
  const oldOptions = ['TRIAL', 'STANDARD', 'PRO'];
  const newOptions = ['STANDARD', 'PRO'];
  
  console.log('修改前选项:', oldOptions);
  console.log('修改后选项:', newOptions);
  console.log('减少选项数量:', oldOptions.length - newOptions.length);
  console.log('简化说明: PRO选项现在代表"专业版/试用版（共享配置）"');
}

/**
 * 运行所有测试
 */
export function runAllTests() {
  console.log('🧪 开始测试前端配置UI修改效果\n');
  
  testFeedbackGrouping();
  testSubscriptionNameDisplay();
  testConfigOptions();
  
  console.log('\n✅ 所有测试完成');
  console.log('\n📋 修改总结:');
  console.log('1. 专业版和试用版配置选项合并');
  console.log('2. 反馈消息分组逻辑优化');
  console.log('3. 订阅计划显示名称更新');
  console.log('4. 添加配置共享提示信息');
  console.log('5. 简化了配置界面的复杂度');
}

// 如果在浏览器环境中，可以在控制台运行测试
if (typeof window !== 'undefined') {
  (window as any).testConfigUI = {
    testFeedbackGrouping,
    testSubscriptionNameDisplay,
    testConfigOptions,
    runAllTests
  };
}
