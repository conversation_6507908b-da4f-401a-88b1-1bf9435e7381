import path from 'path';
import fs from 'fs';
import { IFeedbackMessage } from '../models';
import { CharacterRank, SubscriptionPlan, FocusState, DistractedSubtype } from '../types/character';

/**
 * 生成标准化的音频文件名
 */
function generateStandardAudioFilename(
  subscriptionPlan: SubscriptionPlan,
  studyState: FocusState,
  distractedSubtype: DistractedSubtype | undefined,
  messageIndex: number
): string {
  const plan = subscriptionPlan.toLowerCase();
  let state = studyState.toLowerCase();

  // 处理分心状态的子类型
  if (studyState === FocusState.DISTRACTED && distractedSubtype) {
    state = distractedSubtype.toLowerCase();
  } else if (studyState === FocusState.OFF_SEAT) {
    state = 'off_seat';
  }

  return `${plan}_${state}_${messageIndex + 1}.mp3`;
}

/**
 * 从MD文件解析反馈消息数据
 */
export async function initializeFeedbackFromMd(): Promise<Partial<IFeedbackMessage>[]> {
  try {
    const filePath = path.join(__dirname, '..', '唐僧、观音反馈文案.md');
    console.log('读取文件:', filePath);
    
    if (!fs.existsSync(filePath)) {
      throw new Error(`文件不存在: ${filePath}`);
    }

    const fileContent = fs.readFileSync(filePath, 'utf-8');
    const feedbackData = JSON.parse(fileContent);
    
    const feedbackMessages: Partial<IFeedbackMessage>[] = [];

    // 处理专业版数据（pro）- 专业版和试用版共享配置
    if (feedbackData.pro && feedbackData.pro.wukong) {
      const proData = feedbackData.pro.wukong;

      // 创建共享的专业版配置（试用版和专业版使用相同配置，通过业务逻辑区分）
      const sharedProMessages = createMessagesForPlan(proData, SubscriptionPlan.PRO);
      feedbackMessages.push(...sharedProMessages);

      console.log('专业版和试用版将共享相同的反馈配置');
    }

    // 处理标准版数据（standard）
    if (feedbackData.standard && feedbackData.standard.wukong) {
      const standardData = feedbackData.standard.wukong;
      const standardMessages = createMessagesForPlan(standardData, SubscriptionPlan.STANDARD);
      feedbackMessages.push(...standardMessages);
    }

    console.log(`解析完成，共生成 ${feedbackMessages.length} 条反馈消息`);
    return feedbackMessages;
    
  } catch (error) {
    console.error('从MD文件初始化反馈消息失败:', error);
    throw error;
  }
}

/**
 * 为指定订阅计划创建反馈消息
 */
function createMessagesForPlan(data: any, subscriptionPlan: SubscriptionPlan): Partial<IFeedbackMessage>[] {
  const messages: Partial<IFeedbackMessage>[] = [];
  
  // 状态映射
  const stateMapping = [
    { key: 'focused', studyState: FocusState.FOCUSED },
    { key: 'off_seat', studyState: FocusState.OFF_SEAT },
    { key: 'play', studyState: FocusState.DISTRACTED, distractedSubtype: DistractedSubtype.PLAY },
    { key: 'distracted', studyState: FocusState.DISTRACTED, distractedSubtype: DistractedSubtype.DISTRACTED },
    { key: 'zone', studyState: FocusState.DISTRACTED, distractedSubtype: DistractedSubtype.ZONE },
    { key: 'talk', studyState: FocusState.DISTRACTED, distractedSubtype: DistractedSubtype.TALK },
    { key: 'sleep', studyState: FocusState.DISTRACTED, distractedSubtype: DistractedSubtype.SLEEP }
  ];

  stateMapping.forEach(stateInfo => {
    if (data[stateInfo.key] && Array.isArray(data[stateInfo.key])) {
      const messageTexts = data[stateInfo.key].filter((msg: string) => msg && msg.trim());
      
      if (messageTexts.length > 0) {
        // 生成预期的音频文件名数组
        const audioUrls = messageTexts.map((_: string, index: number) =>
          generateStandardAudioFilename(
            subscriptionPlan,
            stateInfo.studyState,
            stateInfo.distractedSubtype,
            index
          )
        );

        const feedbackMessage: Partial<IFeedbackMessage> = {
          characterRank: CharacterRank.WUKONG,
          subscriptionPlan: subscriptionPlan,
          studyState: stateInfo.studyState,
          messages: messageTexts,
          audioUrls: audioUrls, // 使用预期的音频文件名
          isActive: true
        };

        // 如果有分心子类型，添加到消息中
        if (stateInfo.distractedSubtype) {
          feedbackMessage.distractedSubtype = stateInfo.distractedSubtype;
        }

        messages.push(feedbackMessage);

        console.log(`创建反馈消息: ${subscriptionPlan} - ${stateInfo.studyState}${stateInfo.distractedSubtype ? ` (${stateInfo.distractedSubtype})` : ''} - ${messageTexts.length}条消息`);
        console.log(`预期音频文件: ${audioUrls.join(', ')}`);
      }
    }
  });

  return messages;
}